import fs from "fs"
import path from "path"

// Simple JSON-based database for development
const dbPath = path.join(process.cwd(), "mira-ai-data.json")

// Initialize empty database structure
const initDb = () => {
  if (!fs.existsSync(dbPath)) {
    const initialData = {
      brands: [],
      socialAccounts: [],
      competitors: [],
      brandPosts: [],
      dataSourcesData: [],
      users: [],
      nextIds: {
        brands: 1,
        socialAccounts: 1,
        competitors: 1,
        brandPosts: 1,
        dataSourcesData: 1,
        users: 1
      }
    }
    fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2))
  }
}

// Read database
const readDb = () => {
  initDb()
  return JSON.parse(fs.readFileSync(dbPath, 'utf8'))
}

// Write database
const writeDb = (data: any) => {
  fs.writeFileSync(dbPath, JSON.stringify(data, null, 2))
}

export interface Brand {
  id: number
  name: string
  industry: string
  description: string
  website: string
  target_audience: string
  brand_voice: string
  logo_url?: string
  created_at: string
  updated_at: string
}

export interface SocialAccount {
  id: number
  brand_id: number
  platform: string
  account_handle: string
  access_token?: string
  is_connected: boolean
  last_sync?: string
  created_at: string
}

export interface Competitor {
  id: number
  brand_id: number
  name: string
  platform: string
  handle: string
  industry?: string
  follower_count: number
  last_analyzed?: string
  created_at: string
}

export interface BrandPost {
  id: number
  brand_id: number
  platform: string
  content: string
  hashtags?: string
  scheduled_at?: string
  posted_at?: string
  engagement_score: number
  likes_count: number
  comments_count: number
  shares_count: number
  status: "draft" | "scheduled" | "posted" | "failed"
  created_at: string
  updated_at: string
}

export interface BrandStats {
  total_posts: number
  connected_accounts: number
  competitors_tracked: number
  avg_engagement: number
}

export interface AutomatedSchedule {
  id: number
  brand_id: number
  platform: string
  frequency: "daily" | "weekly" | "monthly"
  days_of_week: string // JSON array of numbers (0-6 for Sunday-Saturday)
  times_of_day: string // JSON array of strings (e.g., "09:00", "14:30")
  content_type?: string
  tone?: string
  is_active: boolean
  last_run?: string
  created_at: string
  updated_at: string
}

export interface DataSource {
  id: number
  brand_id: number
  source_type: string
  source_url?: string
  data_content?: string
  last_updated: string
  is_active: boolean
}

export interface User {
  id: number
  email: string
  password_hash: string
  role: "creator" | "approver" | "admin"
  created_at: string
}

// Brand operations
export const brandOperations = {
  getAll: (): Brand[] => {
    const data = readDb()
    return data.brands.sort((a: Brand, b: Brand) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  },

  getById: (id: number): Brand | undefined => {
    const data = readDb()
    return data.brands.find((brand: Brand) => brand.id === id)
  },

  create: (brand: Omit<Brand, "id" | "created_at" | "updated_at">): Brand => {
    const data = readDb()
    const newBrand: Brand = {
      id: data.nextIds.brands++,
      ...brand,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    data.brands.push(newBrand)
    writeDb(data)
    return newBrand
  },

  getStats: (brandId: number): BrandStats => {
    const data = readDb()
    const brandPosts = data.brandPosts.filter((post: BrandPost) => post.brand_id === brandId)
    const socialAccounts = data.socialAccounts.filter((account: SocialAccount) =>
      account.brand_id === brandId && account.is_connected
    )
    const competitors = data.competitors.filter((competitor: Competitor) => competitor.brand_id === brandId)

    const avgEngagement = brandPosts.length > 0
      ? brandPosts.reduce((sum: number, post: BrandPost) => sum + post.engagement_score, 0) / brandPosts.length
      : 0

    return {
      total_posts: brandPosts.length,
      connected_accounts: socialAccounts.length,
      competitors_tracked: competitors.length,
      avg_engagement: avgEngagement,
    }
  },
}

// Social account operations
export const socialAccountOperations = {
  getByBrandId: (brandId: number): SocialAccount[] => {
    const data = readDb()
    return data.socialAccounts.filter((account: SocialAccount) => account.brand_id === brandId)
  },

  create: (brandId: number, platform: string, accountHandle: string, accessToken: string): SocialAccount => {
    const data = readDb()
    const newAccount: SocialAccount = {
      id: data.nextIds.socialAccounts++,
      brand_id: brandId,
      platform,
      account_handle: accountHandle,
      access_token: accessToken,
      is_connected: true,
      last_sync: new Date().toISOString(),
      created_at: new Date().toISOString(),
    }
    data.socialAccounts.push(newAccount)
    writeDb(data)
    return newAccount
  },

  updateConnectionStatus: (accountId: number, isConnected: boolean): SocialAccount => {
    const data = readDb()
    const accountIndex = data.socialAccounts.findIndex((account: SocialAccount) => account.id === accountId)
    if (accountIndex !== -1) {
      data.socialAccounts[accountIndex].is_connected = isConnected
      data.socialAccounts[accountIndex].last_sync = new Date().toISOString()
      writeDb(data)
      return data.socialAccounts[accountIndex]
    }
    throw new Error('Account not found')
  },

  delete: (accountId: number) => {
    const data = readDb()
    data.socialAccounts = data.socialAccounts.filter((account: SocialAccount) => account.id !== accountId)
    writeDb(data)
  },
}

// Competitor operations
export const competitorOperations = {
  getByBrandId: (brandId: number): Competitor[] => {
    const data = readDb()
    return data.competitors.filter((competitor: Competitor) => competitor.brand_id === brandId)
  },

  create: (competitor: Omit<Competitor, "id" | "created_at" | "last_analyzed">): Competitor => {
    const data = readDb()
    const newCompetitor: Competitor = {
      id: data.nextIds.competitors++,
      ...competitor,
      last_analyzed: new Date().toISOString(),
      created_at: new Date().toISOString(),
    }
    data.competitors.push(newCompetitor)
    writeDb(data)
    return newCompetitor
  },

  delete: (competitorId: number) => {
    const data = readDb()
    data.competitors = data.competitors.filter((competitor: Competitor) => competitor.id !== competitorId)
    writeDb(data)
  },
}

// Brand post operations
export const brandPostOperations = {
  getByBrandId: (brandId: number): BrandPost[] => {
    const data = readDb()
    return data.brandPosts.filter((post: BrandPost) => post.brand_id === brandId)
      .sort((a: BrandPost, b: BrandPost) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
  },

  create: (
    brandId: number,
    platform: string,
    content: string,
    hashtags: string,
    scheduledAt?: string
  ): BrandPost => {
    const data = readDb()
    const newPost: BrandPost = {
      id: data.nextIds.brandPosts++,
      brand_id: brandId,
      platform,
      content,
      hashtags,
      scheduled_at: scheduledAt,
      engagement_score: 0,
      likes_count: 0,
      comments_count: 0,
      shares_count: 0,
      status: scheduledAt ? "scheduled" : "draft",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    data.brandPosts.push(newPost)
    writeDb(data)
    return newPost
  },
}

// User operations
export const userOperations = {
  getByEmail: (email: string): User | undefined => {
    const data = readDb()
    return data.users.find((user: User) => user.email === email)
  },

  create: (email: string, passwordHash: string, role: "creator" | "approver" | "admin" = "creator"): User => {
    const data = readDb()
    const newUser: User = {
      id: data.nextIds.users++,
      email,
      password_hash: passwordHash,
      role,
      created_at: new Date().toISOString(),
    }
    data.users.push(newUser)
    writeDb(data)
    return newUser
  },
}

// Data sources operations
export const dataSourcesOperations = {
  getByBrandId: (brandId: number): DataSource[] => {
    const data = readDb()
    return data.dataSourcesData.filter((source: DataSource) => source.brand_id === brandId)
  },

  create: (brandId: number, sourceType: string, sourceUrl?: string, dataContent?: string): DataSource => {
    const data = readDb()
    const newSource: DataSource = {
      id: data.nextIds.dataSourcesData++,
      brand_id: brandId,
      source_type: sourceType,
      source_url: sourceUrl,
      data_content: dataContent,
      last_updated: new Date().toISOString(),
      is_active: true,
    }
    data.dataSourcesData.push(newSource)
    writeDb(data)
    return newSource
  },
}
