const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

const dbPath = path.join(process.cwd(), 'mira-ai.db');
const schemaPath = path.join(process.cwd(), 'scripts', 'init-database.sql');

function initializeDatabase() {
  if (!fs.existsSync(dbPath)) {
    console.log('Database file not found, initializing new database...');
    const db = new Database(dbPath);
    const schema = fs.readFileSync(schemaPath, 'utf8');
    db.exec(schema);
    db.close();
    console.log('Database initialized and seeded successfully.');
  } else {
    console.log('Database file already exists, skipping initialization.');
  }
}

initializeDatabase();
